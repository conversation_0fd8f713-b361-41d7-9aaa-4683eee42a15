# Ditto 数据导入 PasteBar 指南

## 概述

这个脚本可以将 Ditto 剪贴板管理器的历史数据导入到 PasteBar 中，让你无缝迁移 65,449 条剪贴板记录。

## Ditto 数据库格式分析

你的 `Ditto.db` 文件包含：

- **格式**: SQLite 3.x 数据库 (UTF-16 little endian)
- **记录数量**: 65,449 条剪贴板记录
- **主要表**: Main (主记录), Data (格式数据), CopyBuffers (缓冲区)

### 数据结构
- **Main 表**: 存储剪贴板的基本信息（ID、时间、文本、分组等）
- **Data 表**: 存储不同格式的剪贴板数据（HTML、Unicode文本等）
- **CopyBuffers 表**: 存储复制缓冲区信息

## 支持的数据类型

### ✅ 完全支持
- **文本内容**: 纯文本剪贴板内容
- **HTML 内容**: 富文本和网页内容  
- **Unicode 文本**: 多语言文本支持
- **时间戳**: 原始创建和使用时间
- **收藏状态**: 基于 `lDontAutoDelete` 标志转换
- **内容类型检测**: 自动检测链接、代码、视频等

### ⚠️ 部分支持
- **分组数据**: 跳过分组记录，只导入实际内容
- **快捷键**: Ditto 的快捷键信息不会导入
- **使用统计**: 最后粘贴时间等统计信息不会导入

### ❌ 不支持
- **二进制数据**: 图片等二进制剪贴板内容
- **自定义格式**: 特殊的剪贴板格式
- **分组结构**: PasteBar 使用不同的组织方式

## 使用步骤

### 1. 准备工作

确保你有以下文件：
- Ditto.db 文件路径: `/Users/<USER>/Downloads/Ditto.db`
- PasteBar 数据库路径: 通常在 `~/Library/Application Support/app.anothervision.pasteBar/pastebar-db.data`

### 2. 备份 PasteBar 数据库

**重要**: 在导入前务必备份 PasteBar 数据库！

```bash
cp ~/Library/Application\ Support/app.anothervision.pasteBar/pastebar-db.data ~/Desktop/pastebar-backup.data
```

### 3. 关闭 PasteBar 应用

确保 PasteBar 应用完全关闭，避免数据库锁定。

### 4. 运行导入脚本

```bash
cd /Users/<USER>/pastebar/PasteBarApp/scripts
python3 import_ditto_data.py /Users/<USER>/Downloads/Ditto.db ~/Library/Application\ Support/app.anothervision.pasteBar/pastebar-db.data
```

### 5. 重启 PasteBar

导入完成后，重启 PasteBar 应用以查看导入的数据。

## 数据映射关系

| Ditto 字段 | PasteBar 字段 | 说明 |
|------------|---------------|------|
| `lID` | `history_id` | 重新生成 UUID |
| `mText` | `value` | 剪贴板文本内容 |
| `mText` (前50字符) | `title` | 自动生成标题 |
| `lDate` | `created_at` | Windows FILETIME 转 Unix 时间戳 |
| `lDontAutoDelete` | `is_favorite` | 不自动删除 → 收藏 |
| 自动检测 | `is_text/is_code/is_link/is_video` | 内容类型标志 |
| 自动生成 | `value_preview` | 内容预览（前150字符） |
| 自动计算 | `value_hash` | SHA256 哈希值 |

## 智能内容检测

脚本会自动检测以下内容类型：

### 🔗 链接检测
- HTTP/HTTPS URL
- 自动标记为 `is_link = true`

### 💻 代码检测
检测常见代码模式：
- 函数定义: `function`, `def`, `class`
- 导入语句: `import`, `from`
- SQL 语句: `SELECT`, `INSERT`, `UPDATE`
- HTML 标签: `<html>`, `<script>`
- 代码块: ` ``` `

### 🎬 视频检测
检测视频相关内容：
- YouTube: `youtube.com/watch`, `youtu.be/`
- Vimeo: `vimeo.com/`
- 哔哩哔哩: `bilibili.com/`
- 视频文件: `.mp4`, `.avi`, `.mov`

### 😀 Emoji 检测
- Unicode emoji 字符检测
- 自动标记 `has_emoji = true`

## 预期结果

导入完成后，你将看到：
- ✅ 所有文本剪贴板记录
- ✅ 保持原始时间顺序
- ✅ 智能内容类型标记
- ✅ 收藏状态转换
- ✅ 自动生成的预览和标题

## 性能说明

- **记录数量**: 65,449 条记录
- **预计时间**: 5-10 分钟（取决于系统性能）
- **内存使用**: 适中，逐条处理避免内存溢出
- **进度显示**: 每 100 条记录显示一次进度

## 故障排除

### 常见错误

1. **数据库文件不存在**
   ```
   错误: Ditto 数据库文件不存在: /path/to/Ditto.db
   ```
   - 检查文件路径是否正确
   - 确保文件存在且可读

2. **PasteBar 数据库锁定**
   ```
   database is locked
   ```
   - 确保 PasteBar 应用已完全关闭
   - 等待几秒后重试

3. **权限问题**
   ```
   Permission denied
   ```
   - 检查文件读写权限
   - 使用 `sudo` 运行（不推荐）

4. **重复记录**
   ```
   跳过重复记录: 12345
   ```
   - 这是正常的，脚本会自动跳过重复记录

### 验证导入结果

导入完成后，在 PasteBar 中检查：
1. 历史记录数量是否正确
2. 最新记录的时间是否正确
3. 收藏的记录是否正确标记
4. 内容类型检测是否准确

## 注意事项

1. **数据安全**: 导入前务必备份 PasteBar 数据库
2. **应用关闭**: 导入时确保 PasteBar 完全关闭
3. **时间转换**: Ditto 的 Windows FILETIME 会转换为标准 Unix 时间戳
4. **编码处理**: 自动处理 UTF-16 到 UTF-8 的转换
5. **内容过滤**: 只导入有效的文本内容，跳过空记录和分组

## 技术细节

### 时间戳转换
```python
# Windows FILETIME 转 Unix 时间戳
unix_timestamp = (filetime - 116444736000000000) / 10000000
```

### 内容哈希
```python
# SHA256 哈希计算
value_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()
```

### 预览生成
```python
# 创建 150 字符预览
preview = re.sub(r'\s+', ' ', text.strip())[:150] + "..."
```

导入完成后，你的 PasteBar 将包含所有 Ditto 的历史记录，并且具有智能的内容分类和搜索功能！
