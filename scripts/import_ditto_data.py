#!/usr/bin/env python3
"""
Ditto 数据导入 PasteBar 脚本

使用方法:
python3 import_ditto_data.py /path/to/Ditto.db /path/to/pastebar.db

注意: 请在运行前备份 PasteBar 数据库
"""

import sqlite3
import sys
import os
import hashlib
import time
from datetime import datetime
import uuid
import json
import re

def generate_uuid():
    """生成唯一ID"""
    return str(uuid.uuid4())

def parse_ditto_time(timestamp):
    """解析 Ditto 时间戳"""
    try:
        # Ditto 使用的是 Windows FILETIME 格式 (100-nanosecond intervals since January 1, 1601)
        # 转换为 Unix 时间戳
        unix_timestamp = (timestamp - 116444736000000000) / 10000000
        return int(unix_timestamp * 1000)  # 转换为毫秒时间戳
    except:
        return int(time.time() * 1000)  # 如果解析失败，使用当前时间

def calculate_hash(content):
    """计算内容哈希"""
    if not content:
        return None
    return hashlib.sha256(content.encode('utf-8')).hexdigest()

def detect_content_type(text, clipboard_formats):
    """检测内容类型"""
    is_text = True
    is_code = False
    is_link = False
    is_video = False
    has_emoji = False
    
    if not text:
        return is_text, is_code, is_link, is_video, has_emoji
    
    # 检测链接
    url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
    if re.search(url_pattern, text):
        is_link = True
    
    # 检测视频链接
    video_patterns = [
        r'youtube\.com/watch',
        r'youtu\.be/',
        r'vimeo\.com/',
        r'bilibili\.com/',
        r'\.mp4',
        r'\.avi',
        r'\.mov'
    ]
    for pattern in video_patterns:
        if re.search(pattern, text, re.IGNORECASE):
            is_video = True
            break
    
    # 检测代码
    code_indicators = [
        'function ', 'def ', 'class ', 'import ', 'from ',
        '#!/', '<?php', '<html', '<script', 'SELECT ', 'INSERT ',
        'UPDATE ', 'DELETE ', 'CREATE TABLE', '```', 'console.log'
    ]
    for indicator in code_indicators:
        if indicator in text:
            is_code = True
            break
    
    # 检测 emoji (简单检测)
    emoji_pattern = r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]'
    if re.search(emoji_pattern, text):
        has_emoji = True
    
    return is_text, is_code, is_link, is_video, has_emoji

def create_preview(text, max_length=150):
    """创建预览文本"""
    if not text:
        return None
    
    # 移除多余的空白字符
    preview = re.sub(r'\s+', ' ', text.strip())
    
    if len(preview) <= max_length:
        return preview
    
    return preview[:max_length] + "..."

def import_ditto_to_pastebar(ditto_db_path, pastebar_db_path):
    """导入 Ditto 数据到 PasteBar"""
    
    if not os.path.exists(ditto_db_path):
        print(f"错误: Ditto 数据库文件不存在: {ditto_db_path}")
        return False
    
    if not os.path.exists(pastebar_db_path):
        print(f"错误: PasteBar 数据库文件不存在: {pastebar_db_path}")
        return False
    
    # 连接数据库
    ditto_conn = sqlite3.connect(ditto_db_path)
    paste_conn = sqlite3.connect(pastebar_db_path)
    
    try:
        ditto_cursor = ditto_conn.cursor()
        paste_cursor = paste_conn.cursor()
        
        # 查询 Ditto 主记录
        print("正在查询 Ditto 数据...")
        ditto_cursor.execute("""
            SELECT lID, lDate, mText, bIsGroup, lParentID, 
                   QuickPasteText, lastPasteDate, lDontAutoDelete
            FROM Main 
            WHERE bIsGroup = 0 AND mText IS NOT NULL AND mText != ''
            ORDER BY lDate DESC
        """)
        
        main_records = ditto_cursor.fetchall()
        print(f"找到 {len(main_records)} 条 Ditto 记录")
        
        imported_count = 0
        skipped_count = 0
        
        for record in main_records:
            ditto_id, ditto_date, text, is_group, parent_id, quick_paste_text, last_paste_date, dont_auto_delete = record
            
            if not text or text.strip() == '':
                skipped_count += 1
                continue
            
            # 查询相关的数据格式
            ditto_cursor.execute("""
                SELECT strClipBoardFormat, ooData 
                FROM Data 
                WHERE lParentID = ?
            """, (ditto_id,))
            
            data_records = ditto_cursor.fetchall()
            clipboard_formats = [fmt for fmt, _ in data_records]
            
            # 生成新的 UUID
            history_id = generate_uuid()
            
            # 转换时间戳
            created_at = parse_ditto_time(ditto_date)
            updated_at = created_at
            
            # 创建日期字符串
            created_date = datetime.fromtimestamp(created_at / 1000).strftime('%Y-%m-%d %H:%M:%S')
            updated_date = created_date
            
            # 计算哈希
            value_hash = calculate_hash(text)
            
            # 检测内容类型
            is_text, is_code, is_link, is_video, has_emoji = detect_content_type(text, clipboard_formats)
            
            # 创建预览
            value_preview = create_preview(text)
            
            # 计算预览行数和字符数
            lines = text.split('\n')
            value_more_preview_lines = max(0, len(lines) - 3) if len(lines) > 3 else 0
            value_more_preview_chars = max(0, len(text) - 150) if len(text) > 150 else 0
            
            # 创建标题 (使用前50个字符)
            title = create_preview(text, 50)
            
            # 检测是否为收藏 (根据 dont_auto_delete 标志)
            is_favorite = bool(dont_auto_delete)
            
            # 准备插入数据
            insert_data = {
                'history_id': history_id,
                'title': title,
                'value': text,
                'value_preview': value_preview,
                'value_more_preview_lines': value_more_preview_lines,
                'value_more_preview_chars': value_more_preview_chars,
                'value_hash': value_hash,
                'is_image': False,
                'image_path_full_res': None,
                'image_data_low_res': None,
                'image_preview_height': None,
                'image_height': None,
                'image_width': None,
                'image_data_url': None,
                'image_hash': None,
                'is_image_data': False,
                'is_masked': False,
                'is_text': is_text,
                'is_code': is_code,
                'is_link': is_link,
                'is_video': is_video,
                'has_emoji': has_emoji,
                'has_masked_words': False,
                'links': None,
                'is_pinned': False,
                'is_favorite': is_favorite,
                'detected_language': None,
                'pinned_order_number': None,
                'created_at': created_at,
                'updated_at': updated_at,
                'created_date': created_date,
                'updated_date': updated_date,
                'history_options': None,
                'copied_from_app': None
            }
            
            # 插入到 PasteBar 数据库
            try:
                paste_cursor.execute("""
                    INSERT INTO clipboard_history (
                        history_id, title, value, value_preview, value_more_preview_lines,
                        value_more_preview_chars, value_hash, is_image, image_path_full_res,
                        image_data_low_res, image_preview_height, image_height, image_width,
                        image_data_url, image_hash, is_image_data, is_masked, is_text,
                        is_code, is_link, is_video, has_emoji, has_masked_words, links,
                        is_pinned, is_favorite, detected_language, pinned_order_number,
                        created_at, updated_at, created_date, updated_date, history_options,
                        copied_from_app
                    ) VALUES (
                        :history_id, :title, :value, :value_preview, :value_more_preview_lines,
                        :value_more_preview_chars, :value_hash, :is_image, :image_path_full_res,
                        :image_data_low_res, :image_preview_height, :image_height, :image_width,
                        :image_data_url, :image_hash, :is_image_data, :is_masked, :is_text,
                        :is_code, :is_link, :is_video, :has_emoji, :has_masked_words, :links,
                        :is_pinned, :is_favorite, :detected_language, :pinned_order_number,
                        :created_at, :updated_at, :created_date, :updated_date, :history_options,
                        :copied_from_app
                    )
                """, insert_data)
                
                imported_count += 1
                
                if imported_count % 100 == 0:
                    print(f"已导入 {imported_count} 条记录...")
                    
            except sqlite3.IntegrityError as e:
                print(f"跳过重复记录: {ditto_id}")
                skipped_count += 1
            except Exception as e:
                print(f"导入记录失败 {ditto_id}: {e}")
                skipped_count += 1
        
        # 提交事务
        paste_conn.commit()
        
        print(f"\n导入完成!")
        print(f"成功导入: {imported_count} 条记录")
        print(f"跳过记录: {skipped_count} 条")
        
        return True
        
    except Exception as e:
        print(f"导入过程中发生错误: {e}")
        paste_conn.rollback()
        return False
        
    finally:
        ditto_conn.close()
        paste_conn.close()

def main():
    if len(sys.argv) != 3:
        print("使用方法: python3 import_ditto_data.py <Ditto.db路径> <PasteBar数据库路径>")
        print("示例: python3 import_ditto_data.py /Users/<USER>/Downloads/Ditto.db /Users/<USER>/Library/Application\\ Support/app.anothervision.pasteBar/pastebar-db.data")
        sys.exit(1)
    
    ditto_db_path = sys.argv[1]
    pastebar_db_path = sys.argv[2]
    
    print("Ditto 数据导入 PasteBar")
    print("=" * 50)
    print(f"源文件: {ditto_db_path}")
    print(f"目标文件: {pastebar_db_path}")
    print()
    
    # 确认操作
    response = input("确认开始导入? (y/N): ")
    if response.lower() != 'y':
        print("导入已取消")
        sys.exit(0)
    
    success = import_ditto_to_pastebar(ditto_db_path, pastebar_db_path)
    
    if success:
        print("\n导入成功! 请重启 PasteBar 应用以查看导入的数据。")
    else:
        print("\n导入失败! 请检查错误信息并重试。")

if __name__ == "__main__":
    main()
