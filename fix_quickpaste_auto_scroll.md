# PasteBar 快速粘贴窗口自动跳回问题修复

## 问题描述
快速粘贴窗口呼出后，用户往下拉查询历史记录时，窗口会自动跳回最新的历史记录，导致用户无法正常浏览历史内容。

## 问题根本原因

### 1. 剪贴板监听器强制滚动
在 `QuickPasteApp.tsx` 中，剪贴板监听器会在检测到剪贴板更新时强制滚动到顶部：

```typescript
listen('clipboard://clipboard-monitor/update', async e => {
  if (e.payload === 'clipboard update') {
    // ... 更新数据 ...
    
    setTimeout(() => {
      clipboardHistoryStore.scrollToTopHistoryList()  // 强制滚动到顶部
    }, 100)
  }
})
```

### 2. 缺少用户滚动状态跟踪
系统没有跟踪用户是否主动滚动了列表，导致无法区分自动滚动和用户滚动。

### 3. 窗口可见性检查不足
没有检查快速粘贴窗口是否可见，即使窗口隐藏时也会执行滚动操作。

## 修复方案

### 1. 智能窗口可见性检查
修改剪贴板监听器，只在窗口不可见时才自动滚动：

```typescript
setTimeout(async () => {
  try {
    const isVisible = await appWindow.isVisible()
    if (!isVisible) {
      // 窗口隐藏时才滚动，为下次打开做准备
      clipboardHistoryStore.scrollToTopHistoryList()
    }
    // 窗口可见时不滚动，保持用户的滚动位置
  } catch (error) {
    console.error('Failed to check window visibility:', error)
    // 出错时不滚动，避免打扰用户
  }
}, 100)
```

### 2. 用户滚动状态跟踪
在 `clipboardHistoryStore` 中添加用户滚动状态：

```typescript
interface ClipboardHistoryStoreState {
  userHasScrolled: boolean
  setUserHasScrolled: (hasScrolled: boolean) => void
  // ... 其他属性
}
```

### 3. 滚动事件监听
在快速粘贴页面中监听滚动事件，跟踪用户滚动行为：

```typescript
const onScrollCallback = throttle((event: Event) => {
  const target = event.target as HTMLElement
  if (target && target.scrollTop > 50) {
    // 用户向下滚动超过50px，标记为用户滚动
    clipboardHistoryStore.getState().setUserHasScrolled(true)
  } else if (target && target.scrollTop === 0) {
    // 用户滚动回顶部，重置标记
    clipboardHistoryStore.getState().setUserHasScrolled(false)
  }
}, 300, { leading: true })
```

### 4. 智能滚动决策
改进 `scrollToTopHistoryList` 函数，综合考虑窗口可见性和用户滚动状态：

```typescript
async scrollToTopHistoryList(force = false) {
  const { historyListSimpleBar, userHasScrolled } = get()
  
  if (!force && window.location.pathname === '/quickpaste-index') {
    try {
      const { appWindow } = await import('@tauri-apps/api/window')
      const isVisible = await appWindow.isVisible()
      
      if (isVisible && userHasScrolled) {
        // 窗口可见且用户已滚动，保持当前位置
        console.log('Preserving user scroll position')
        return
      }
    } catch (error) {
      // 出错时，如果用户已滚动则不自动滚动
      if (userHasScrolled) return
    }
  }
  
  // 执行滚动并重置用户滚动状态
  historyListSimpleBar?.current.scrollTo({ top: 0, behavior: 'auto' })
  get().setUserHasScrolled(false)
}
```

## 修复效果

### ✅ 解决的问题
1. **用户滚动不被打断**：用户浏览历史记录时不会被强制跳回顶部
2. **智能自动滚动**：只在合适的时机（窗口隐藏或用户未滚动）才自动滚动
3. **保持用户体验**：用户的滚动位置得到尊重和保持

### 🎯 工作原理
1. **窗口隐藏时**：正常自动滚动到顶部，为下次打开做准备
2. **窗口可见且用户未滚动**：允许自动滚动（如新剪贴板内容）
3. **窗口可见且用户已滚动**：保持用户的滚动位置，不自动滚动
4. **用户滚动回顶部**：重置滚动状态，允许后续自动滚动

### 📋 测试场景

#### 场景1：正常使用（修复前的问题）
1. 打开快速粘贴窗口
2. 向下滚动查看历史记录
3. 复制新内容到剪贴板
4. **预期结果**：滚动位置保持不变，不跳回顶部

#### 场景2：窗口重新打开
1. 关闭快速粘贴窗口
2. 复制新内容到剪贴板
3. 重新打开快速粘贴窗口
4. **预期结果**：显示最新内容在顶部

#### 场景3：用户主动回到顶部
1. 向下滚动查看历史记录
2. 手动滚动回到顶部
3. 复制新内容到剪贴板
4. **预期结果**：允许自动滚动显示新内容

## 技术细节

### 关键文件修改
- `packages/pastebar-app-ui/src/QuickPasteApp.tsx`：剪贴板监听器优化
- `packages/pastebar-app-ui/src/store/clipboardHistoryStore.ts`：滚动状态管理
- `packages/pastebar-app-ui/src/pages/main/ClipboardHistoryQuickPastePage.tsx`：滚动事件监听

### 性能优化
- 使用 `throttle` 限制滚动事件频率（300ms）
- 异步窗口可见性检查，避免阻塞UI
- 智能状态重置，避免内存泄漏

### 兼容性
- 支持所有平台（macOS、Windows、Linux）
- 向后兼容现有功能
- 不影响主窗口的滚动行为

这个修复确保了快速粘贴窗口的用户体验更加流畅，用户可以自由浏览历史记录而不被自动滚动打断。
