<?xml version="1.0" encoding="utf-8"?>
<svg width="32" height="32" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <path d="M 12.887 0.218 C 14.251 0.218 15.374 1.249 15.519 2.575 L 17.596 2.575 C 18.99 2.574 20.146 3.653 20.241 5.044 L 20.247 5.225 C 20.246 5.666 19.921 6.039 19.485 6.1 L 19.362 6.109 C 18.921 6.108 18.549 5.783 18.488 5.347 L 18.48 5.225 C 18.48 4.784 18.154 4.41 17.716 4.35 L 17.596 4.342 L 15.084 4.342 C 14.593 5.074 13.769 5.512 12.887 5.512 L 8.757 5.512 C 7.875 5.513 7.051 5.074 6.559 4.343 L 4.048 4.342 C 3.606 4.342 3.232 4.668 3.172 5.105 L 3.164 5.225 L 3.164 21.136 C 3.164 21.584 3.496 21.953 3.927 22.011 L 4.048 22.02 L 7.582 22.02 C 8.023 22.02 8.397 22.345 8.457 22.782 L 8.466 22.902 C 8.466 23.39 8.07 23.786 7.582 23.786 L 4.048 23.786 C 2.654 23.786 1.499 22.708 1.403 21.318 L 1.397 21.136 L 1.397 5.225 C 1.397 3.832 2.476 2.676 3.866 2.58 L 4.048 2.575 L 6.125 2.575 C 6.273 1.233 7.407 0.218 8.757 0.218 L 12.887 0.218 Z M 19.952 7.287 C 21.346 7.287 22.502 8.366 22.597 9.756 L 22.603 9.938 L 22.603 21.13 C 22.603 22.524 21.524 23.68 20.134 23.775 L 19.952 23.781 L 12.295 23.781 C 10.901 23.781 9.745 22.702 9.65 21.312 L 9.644 21.13 L 9.644 9.938 C 9.644 8.544 10.723 7.388 12.113 7.293 L 12.295 7.287 L 19.952 7.287 Z M 19.952 9.054 L 12.295 9.054 C 11.853 9.054 11.479 9.38 11.419 9.818 L 11.411 9.938 L 11.411 21.13 C 11.411 21.578 11.743 21.948 12.174 22.006 L 12.295 22.014 L 19.952 22.014 C 20.394 22.014 20.768 21.688 20.828 21.25 L 20.836 21.13 L 20.836 9.938 C 20.836 9.496 20.51 9.123 20.073 9.063 L 19.952 9.054 Z M 12.887 1.985 L 8.757 1.985 C 8.08 2.01 7.683 2.759 8.043 3.333 C 8.198 3.58 8.465 3.734 8.757 3.744 L 12.887 3.744 C 13.564 3.769 14.014 3.052 13.697 2.453 C 13.539 2.155 13.224 1.973 12.887 1.985 L 12.887 1.985 Z" style=""/>
  <g transform="matrix(0.153273, 0, 0, 0.166612, 14.348209, 11.441401)" style="">
    <path fill-rule="evenodd" d="M -9.068 44.336 C -9.068 43.459 -8.115 42.749 -6.936 42.749 L 31.455 42.749 C 33.096 42.749 34.123 44.071 33.303 45.129 C 32.921 45.62 32.217 45.923 31.455 45.923 L -6.936 45.923 C -8.115 45.923 -9.068 45.212 -9.068 44.336 Z" style="stroke: rgb(0, 0, 0); fill: rgb(0, 0, 0); stroke-width: 3.53559px;"/>
    <path fill-rule="evenodd" d="M -9.068 4.807 C -9.068 3.931 -8.115 3.22 -6.936 3.22 L 31.455 3.22 C 33.096 3.22 34.123 4.542 33.303 5.6 C 32.921 6.091 32.217 6.393 31.455 6.393 L -6.936 6.393 C -8.115 6.393 -9.068 5.683 -9.068 4.807 Z" style="stroke: rgb(0, 0, 0); fill: rgb(0, 0, 0); stroke-width: 3.53559px;"/>
    <path fill-rule="evenodd" d="M -9.068 24.571 C -9.068 23.695 -8.115 22.985 -6.936 22.985 L 31.455 22.985 C 33.096 22.985 34.123 24.307 33.303 25.365 C 32.921 25.856 32.217 26.158 31.455 26.158 L -6.936 26.158 C -8.115 26.158 -9.068 25.448 -9.068 24.571 Z" style="stroke: rgb(0, 0, 0); fill: rgb(0, 0, 0); stroke-width: 3.53559px;"/>
  </g>
  <g fill="none" stroke="#888888" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="matrix(0.166612, 0, 0, 0.166612, 14.40597, 11.442867)" style=""/>
</svg>