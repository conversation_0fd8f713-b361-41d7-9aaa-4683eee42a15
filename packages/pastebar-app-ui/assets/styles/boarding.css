.boarding-coutout-svg-animated,
div#boarding-popover-item.boarding-popover-item-animated {
  animation: boardingFadeIn 0.4s;
}

body .boarding-coutout-svg {
  display: block;
  pointer-events: none;
  cursor: not-allowed;
  position: absolute;
  border-radius: 10px;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

body.boarding-strict-pointer-events,
body.boarding-strict-pointer-events
  *:not(.boarding-highlighted-element *):not(#boarding-popover-item *):not(
    .boarding-highlighted-element
  ):not(#boarding-popover-item):not(.boarding-coutout-svg path),
body.boarding-no-pointer-events,
body.boarding-no-pointer-events
  *:not(.boarding-highlighted-element *):not(#boarding-popover-item *):not(
    .boarding-highlighted-element
  ):not(#boarding-popover-item):not(.boarding-coutout-svg):not(
    .boarding-coutout-svg path
  ) {
  pointer-events: none !important;
}
/* enable pointer-events where it makes sense */
div#boarding-popover-item,
.boarding-coutout-svg path,
body.boarding-strict-pointer-events .boarding-highlighted-element,
body.boarding-no-pointer-events .boarding-coutout-svg {
  pointer-events: auto !important;
}

/* popover */
div#boarding-popover-item {
  --boarding-popover-padding: 10px;
  --boarding-popover-tip-padding: 15px;
  padding: var(--boarding-popover-padding);
  padding-left: 15px;
  padding-right: 15px;
  display: block;
  position: fixed;
  z-index: 1000000000;
}

/* tip */
.dark div#boarding-popover-item .boarding-popover-tip {
  border: 6px solid;
  content: '';
  position: absolute;
  border-color: rgb(70, 81, 97);
}

div#boarding-popover-item .boarding-popover-tip {
  border: 6px solid rgb(249 250 251);
  content: '';
  position: absolute;
  border-color: rgb(249 250 251);
}

div#boarding-popover-item
  .boarding-popover-tip:not([class^='boarding-tipside-']):not(
    [class^='boarding-tipalign-']
  ):not([class*=' boarding-tipalign-']):not([class*=' boarding-tipalign-']) {
  display: none;
}

/* tb */
div#boarding-popover-item .boarding-popover-tip.boarding-tipside-top {
  top: 100%;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
}
div#boarding-popover-item .boarding-popover-tip.boarding-tipside-bottom {
  bottom: 100%;
  border-left-color: transparent;
  border-top-color: transparent;
  border-right-color: transparent;
}
div#boarding-popover-item
  .boarding-popover-tip.boarding-tipside-top.boarding-tipalign-center,
div#boarding-popover-item
  .boarding-popover-tip.boarding-tipside-bottom.boarding-tipalign-center {
  left: 50%;
  margin-left: -5px;
}
div#boarding-popover-item
  .boarding-popover-tip.boarding-tipside-top.boarding-tipalign-start,
div#boarding-popover-item
  .boarding-popover-tip.boarding-tipside-bottom.boarding-tipalign-start {
  left: var(--boarding-popover-tip-padding);
}
div#boarding-popover-item
  .boarding-popover-tip.boarding-tipside-top.boarding-tipalign-end,
div#boarding-popover-item
  .boarding-popover-tip.boarding-tipside-bottom.boarding-tipalign-end {
  right: var(--boarding-popover-tip-padding);
}

div#boarding-popover-item .boarding-footer-counter {
  font-size: 0.85rem;
  color: #999;
}

/* lr */
div#boarding-popover-item .boarding-popover-tip.boarding-tipside-left {
  left: 100%;
  margin-top: 5px;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-top-color: transparent;
}
div#boarding-popover-item .boarding-popover-tip.boarding-tipside-right {
  right: 100%;
  margin-top: 5px;
  border-left-color: transparent;
  border-bottom-color: transparent;
  border-top-color: transparent;
}
div#boarding-popover-item
  .boarding-popover-tip.boarding-tipside-left.boarding-tipalign-center,
div#boarding-popover-item
  .boarding-popover-tip.boarding-tipside-right.boarding-tipalign-center {
  top: 50%;
  margin-top: -5px;
}
div#boarding-popover-item
  .boarding-popover-tip.boarding-tipside-left.boarding-tipalign-start,
div#boarding-popover-item
  .boarding-popover-tip.boarding-tipside-right.boarding-tipalign-start {
  top: var(--boarding-popover-tip-padding);
}
div#boarding-popover-item
  .boarding-popover-tip.boarding-tipside-left.boarding-tipalign-end,
div#boarding-popover-item
  .boarding-popover-tip.boarding-tipside-right.boarding-tipalign-end {
  bottom: var(--boarding-popover-tip-padding);
}

@keyframes boardingFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Boarding theme */

div#boarding-popover-item {
  margin: 0;
  border-radius: 5px;
  min-width: 250px;
  max-width: 300px;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.4);
}
div#boarding-popover-item .boarding-popover-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}
div#boarding-popover-item .boarding-popover-footer button {
  display: inline-block;
  padding: 3px 10px;
  text-decoration: none;
  cursor: pointer;
  outline: 0;
}

div#boarding-popover-item .boarding-popover-footer button.boarding-disabled {
  cursor: default;
  pointer-events: none;
}
div#boarding-popover-item .boarding-popover-footer .boarding-close-btn {
  position: absolute;
  color: #999;
  top: 4px;
  right: 0;
}
.dark div#boarding-popover-item .boarding-popover-footer .boarding-close-btn {
  color: #cccc;
}

.dark div#boarding-popover-item .boarding-popover-footer .boarding-close-btn:hover {
  color: #fff;
}

div#boarding-popover-item .boarding-popover-footer .boarding-close-btn:hover {
  color: #333;
}

div#boarding-popover-item .boarding-popover-footer .boarding-btn-group {
  display: flex;
  gap: 10px;
  justify-content: end;
}

div#boarding-popover-item .boarding-btn-group button {
  border-radius: 6px;
  padding: 3px 12px;
  border: 1px solid transparent;
}

div#boarding-popover-item .boarding-btn-group button:hover {
  border: 1px solid #007bff;
}

div#boarding-popover-item .boarding-btn-group button.boarding-prev-btn {
  opacity: 0.8;
  color: #0056b3;
}

.dark div#boarding-popover-item .boarding-btn-group button.boarding-prev-btn {
  opacity: 0.8;
  color: #007bff;
}

div#boarding-popover-item .boarding-btn-group button.boarding-prev-btn.boarding-disabled {
  display: none !important;
}

div#boarding-popover-item .boarding-btn-group button.boarding-prev-btn:hover {
  opacity: 1;
  color: #0056b3;
  border: 1px solid #0056b3;
}

.dark div#boarding-popover-item .boarding-btn-group button.boarding-prev-btn:hover {
  opacity: 1;
  color: #007bff;
  border: 1px solid #007bff;
}

button.boarding-next-btn,
div#boarding-popover-item .boarding-btn-group button.boarding-next-btn {
  color: #fff;
  background-color: #0056b3;
}

button.boarding-next-btn,
div#boarding-popover-item .boarding-btn-group button.boarding-next-btn:hover {
  background-color: #007bff;
}

div#boarding-popover-item .boarding-popover-title {
  color: #0056b3;
  font-size: 16px;
  font-weight: bold;
  margin-top: 2px;
  margin-bottom: 6px;
}

.dark div#boarding-popover-item .boarding-popover-title {
  color: #70b5ff;
}

div#boarding-popover-item .boarding-popover-description {
  font-size: 0.95rem;
  margin-bottom: 0;
  font-weight: normal;
}
