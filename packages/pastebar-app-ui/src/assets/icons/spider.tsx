import { SVGProps } from 'react'

export default function WebSpiderIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={props.width ?? '24px'}
      height={props.height ?? '24px'}
      className={props.className}
      onClick={props.onClick}
      viewBox="0 0 24 24"
      fill="currentColor"
      stroke="none"
      strokeWidth="1.5"
    >
      <path d="M22.968 10.69h-5.797c-.074-.148-.223-.37-.297-.52l3.865-2.304c.297-.148.445-.446.445-.818V2.291c0-.52-.445-.966-.966-.966-.519 0-.967.446-.967.966v4.237l-3.79 2.23c-.074-.074-.223-.149-.297-.222.223-.447.372-.968.372-1.563 0-2.007-1.636-3.568-3.569-3.568C9.96 3.405 8.4 5.041 8.4 6.973c0 .522.148 1.041.296 1.563-.073.073-.223.148-.296.222l-3.791-2.23V2.291c0-.52-.446-.966-.967-.966-.521 0-.967.446-.967.966v4.757c0 .298.15.67.446.818l3.866 2.304c-.148.15-.223.372-.298.52H.966c-.52 0-.966.446-.966.967v6.765c0 .519.446.966.966.966.521 0 .966-.447.966-.966v-5.799h4.163c-.074.297-.074.595-.074.967l-2.379.817c-.223.075-.446.223-.521.52-.073.223-.148.521 0 .744L5.5 21.915c.15.371.521.594.893.594.073 0 .223 0 .296-.073.446-.15.745-.744.522-1.19l-2.007-5.353 1.114-.372c.743 2.529 3.047 4.386 5.798 4.386s5.055-1.857 5.798-4.386l1.114.372-2.007 5.353c-.147.446.076 1.04.522 1.19.073.073.223.073.296.073.372 0 .743-.223.893-.594l2.379-6.244c.073-.223.073-.521 0-.744-.075-.223-.298-.371-.521-.52l-2.379-.817c0-.298-.074-.67-.074-.967H22.3v5.799c0 .519.445.966.966.966.52 0 .966-.447.966-.966v-6.765c-.372-.521-.818-.967-1.264-.967ZM11.967 17.9c-2.304 0-4.162-1.857-4.162-4.236 0-1.71 1.04-3.271 2.602-3.939.297-.15.52-.373.594-.67.074-.297 0-.595-.223-.818-.297-.296-.52-.742-.52-1.189 0-.966.743-1.709 1.709-1.709.967 0 1.71.743 1.71 1.709 0 .447-.149.893-.52 1.189-.223.223-.298.521-.223.818.073.297.297.52.594.67 1.562.668 2.602 2.155 2.602 3.865 0 2.453-1.858 4.31-4.163 4.31Z" />
    </svg>
  )
}
