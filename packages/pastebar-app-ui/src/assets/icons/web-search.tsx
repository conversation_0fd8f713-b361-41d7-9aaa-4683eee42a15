import { SVGProps } from 'react'

export default function WebSearchIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={props.width ?? '24px'}
      height={props.height ?? '24px'}
      className={props.className}
      onClick={props.onClick}
      viewBox="0 0 24 24"
      fill="currentColor"
      stroke="none"
      strokeWidth="1.5"
    >
      <path d="M21.219.805H2.681C1.145.805-.1 2.05-.1 3.586V20.27c0 1.536 1.245 2.781 2.781 2.781h18.538c1.535 0 2.781-1.245 2.781-2.781V3.586C24 2.05 22.754.805 21.219.805ZM1.754 3.586c0-.512.414-.927.927-.927h18.538c.512 0 .927.415.927.927v2.78H1.754v-2.78Zm19.465 17.611H2.681a.9257.9257 0 0 1-.927-.927V8.22h20.392v12.05c0 .513-.415.927-.927.927Z" />
      <path d="M5.086 3.782c-.504-.506-1.366-.279-1.553.41a.9079.9079 0 0 0-.032.248c.006.245.102.48.268.659.09.082.193.148.306.194.225.099.48.099.705 0 .112-.047.215-.113.306-.194.081-.09.147-.194.194-.306a.8739.8739 0 0 0 0-.705.982.982 0 0 0-.194-.306Zm2.78 0a.9455.9455 0 0 0-.834-.25.5179.5179 0 0 0-.176.056.5145.5145 0 0 0-.157.083 1.03 1.03 0 0 0-.149.111.947.947 0 0 0-.194.306c-.1.225-.1.48 0 .705.046.112.112.216.194.306.09.082.193.148.306.194.224.099.479.099.705 0 .112-.047.215-.113.305-.194.082-.09.148-.194.195-.306.051-.11.076-.231.074-.353a.9526.9526 0 0 0-.074-.352.967.967 0 0 0-.195-.306Zm1.465 0c-.083.091-.148.194-.195.306a.5303.5303 0 0 0-.055.176c-.011.059-.018.118-.019.176 0 .122.026.241.074.353.047.112.112.216.195.306.365.358.95.358 1.316 0 .082-.09.148-.194.195-.306.051-.11.076-.231.074-.353.004-.712-.767-1.163-1.386-.81a.8972.8972 0 0 0-.199.152Zm5.558 12.83c1.967-2.976-.025-6.966-3.586-7.182-3.561-.215-6.02 3.505-4.427 6.697 1.243 2.489 4.381 3.33 6.702 1.796l2.351 2.35c.513.496 1.37.251 1.543-.442a.93.93 0 0 0-.232-.868l-2.351-2.351Zm-5.832-.589c-1.515-1.513-.822-4.098 1.246-4.653 2.067-.554 3.96 1.338 3.405 3.406-.126.472-.374.902-.719 1.247-1.101 1.05-2.832 1.05-3.932 0Z" />
    </svg>
  )
}
