import { SVGProps } from 'react'

export default function WebScraperIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={props.width ?? '24px'}
      height={props.height ?? '24px'}
      className={props.className}
      onClick={props.onClick}
      viewBox="0 0 24 24"
      fill="currentColor"
      stroke="none"
      strokeWidth="1.5"
    >
      <path d="M12.008 1.666c-7.955 0-12.926 8.613-8.949 15.501 3.977 6.889 13.92 6.889 17.897 0 .907-1.57 1.386-3.352 1.386-5.166 0-5.707-4.627-10.335-10.334-10.335Zm-9.27 10.855h3.169c.035 1.322.263 2.634.675 3.893H3.829a9.1934 9.1934 0 0 1-1.091-3.893Zm9.789-5.973V2.913a8.3791 8.3791 0 0 1 3.375 3.635h-3.375Zm3.817 1.039c.447 1.251.693 2.566.727 3.893h-4.544V7.587h3.817Zm-4.856-4.648v3.609H8.114a8.377 8.377 0 0 1 3.374-3.609Zm0 4.674v3.867H6.944c.035-1.327.28-2.642.728-3.893l3.816.026ZM5.907 11.48H2.738c.07-1.362.443-2.693 1.091-3.893h2.753a13.692 13.692 0 0 0-.675 3.893Zm1.037 1.041h4.544v3.893H7.672a12.5295 12.5295 0 0 1-.728-3.893Zm4.544 4.932v3.635a8.3769 8.3769 0 0 1-3.374-3.635h3.374Zm1.039 3.635v-3.635h3.375a8.3771 8.3771 0 0 1-3.375 3.608v.027Zm0-4.674v-3.893h4.544c-.034 1.327-.28 2.642-.727 3.893h-3.817Zm5.582-3.893h3.169a9.2368 9.2368 0 0 1-1.091 3.893h-2.754c.412-1.259.639-2.571.676-3.893Zm0-1.041a13.7635 13.7635 0 0 0-.676-3.893h2.754a9.2327 9.2327 0 0 1 1.091 3.893h-3.169Zm1.428-4.932h-2.493a9.8857 9.8857 0 0 0-2.543-3.506 9.3305 9.3305 0 0 1 5.01 3.506h.026ZM9.514 3.069A9.9123 9.9123 0 0 0 6.97 6.548H4.504a9.3282 9.3282 0 0 1 5.01-3.479Zm-5.01 14.384h2.493a9.8678 9.8678 0 0 0 2.543 3.505 9.3285 9.3285 0 0 1-5.036-3.505Zm9.997 3.479a9.894 9.894 0 0 0 2.543-3.479h2.493a9.3153 9.3153 0 0 1-5.036 3.479Z" />
    </svg>
  )
}
