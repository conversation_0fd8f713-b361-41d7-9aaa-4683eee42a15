import { SVGProps } from 'react'

export default function BlankIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={props.width ?? '24px'}
      height={props.height ?? '24px'}
      className={props.className}
      viewBox="0 0 24 24"
      onClick={props.onClick}
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="18" height="18" x="3" y="3" rx="2" />
      <path
        fill="currentColor"
        stroke="none"
        d="m18.4044 17.0597-1.3474 1.3474H8.2988l1.3474-1.3474h8.7582ZM14.52 5.0053l3.811 3.811-.5695.7343-1.0281 1.3106-.6031.7568-.5374.6632-.3597.436-.4222.4997-.2734.3133-.2364.2606-.1994.2081-.4734.4683-.9742.9883-.514.5288-.1854-.0092-.1944-.003c-.3311 0-.699.0257-1.1035.077l-.2536.0356-.2478.0408-.237.0439-.3247.067-.6212.1496-2.021 1.0105-1.3475.3369.3369-1.3474 1.0105-2.0211.032-.076.0564-.1558.0474-.1502.0516-.187a6.8003 6.8003 0 0 0 .1495-.7784 10.6177 10.6177 0 0 0 .088-1.797l.1045-.1176.1065-.1134.497-.5086 1.1954-1.1882.3684-.3535.4097-.3752.3575-.3158.3839-.3296.4103-.3434.4366-.3573.4631-.3711.7442-.5827.529-.4057.5556-.4196.5819-.4335ZM8.7067 12.357a11.8664 11.8664 0 0 1-.036.4396l-.0472.4013a8.6212 8.6212 0 0 1-.112.649l-.1351.572.5888.5408.5814-.0902c.3209-.0649.6365-.1185.9407-.157.1784-.0227.3517-.041.52-.055L8.7067 12.357Zm5.6858-5.5734-.19.144-.7708.5933-.4798.3773-.4525.3626-.425.3476-.3975.3325-.3696.3172-.3416.3015-.4179.383-.3182.3058-.8657.8602 2.861 2.861.1762-.1794.5993-.5981.1285-.1254.174-.181.2194-.2415.2594-.297.412-.4874.3522-.4267.5305-.655.5967-.7486.8638-1.1014-2.1444-2.1445Z"
      />
    </svg>
  )
}
