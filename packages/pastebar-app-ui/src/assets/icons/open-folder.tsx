import { SVGProps } from 'react'

export default function OpenFolderIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={props.width ?? '24px'}
      height={props.height ?? '24px'}
      className={props.className}
      onClick={props.onClick}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="1.5"
    >
      {/* <path d="M2 9.5v-4c0-1.1.9-2 2-2h3.93c.669.003 1.292.341 1.66.9l.82 1.2c.368.559.991.897 1.66.9h6.337c1.105 0 2 .895 2 2v10c0 1.105-.895 2-2 2H2" />
      <path d="m9.37 16.5 3-3-3-3" />
      <path d="M2 16.5v-1c0-1.105.895-2 2-2h6" /> */}
      <path d="m6.391 14.336 1.402-2.71a1.867 1.867 0 0 1 1.626-1.028h10.056c1.223-.002 2.119 1.152 1.813 2.337l-1.439 5.607c-.215.83-.966 1.408-1.823 1.402H4.522c-1.033 0-1.87-.837-1.87-1.869V5.925c0-1.032.837-1.869 1.87-1.869h3.644c.636-.006 1.23.311 1.58.841l.757 1.122c.346.525.932.841 1.561.841h5.542c1.032 0 1.869.837 1.869 1.869v1.869" />
      {/* <path d="M19.645 20.123c1.056 0 1.911-.855 1.911-1.911V8.655c0-1.056-.855-1.911-1.911-1.911h-7.549c-.65.007-1.258-.317-1.615-.86l-.775-1.147c-.353-.537-.952-.86-1.595-.86H4.355c-1.056 0-1.911.855-1.911 1.911v12.424c0 1.056.855 1.911 1.911 1.911h15.29Z" /> */}
      {/* <path d="M20 20.5c1.105 0 2-.895 2-2v-10c0-1.105-.895-2-2-2h-7.9c-.68.007-1.316-.332-1.69-.9L9.6 4.4c-.37-.562-.997-.9-1.67-.9H4c-1.105 0-2 .895-2 2v13c0 1.105.895 2 2 2h16Z" /> */}
    </svg>
  )
}
