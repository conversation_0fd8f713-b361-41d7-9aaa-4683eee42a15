import { SVGProps } from 'react'

export default function WebRequestIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={props.width ?? '24px'}
      height={props.height ?? '24px'}
      className={props.className}
      onClick={props.onClick}
      viewBox="0 0 24 24"
      fill="currentColor"
      strokeWidth="1.8"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        stroke="none"
        d="M3.564 1.745c-.272.011-.521.142-.688.355C1.311 4.127.456 6.622.456 9.185c0 2.654.906 5.222 2.576 7.286.318.393.894.451 1.288.131.39-.315.449-.887.134-1.279-1.404-1.736-2.168-3.902-2.168-6.138 0-2.158.712-4.258 2.034-5.967.307-.4.234-.971-.164-1.281-.169-.132-.378-.197-.592-.192Zm16.906.005a.8757.8757 0 0 0-.531.187c-.4.304-.474.879-.169 1.281 1.324 1.709 2.035 3.809 2.035 5.967 0 2.236-.763 4.402-2.168 6.138a.9087.9087 0 0 0 .133 1.279c.394.32.97.262 1.286-.131 1.668-2.064 2.574-4.632 2.574-7.286 0-2.563-.845-5.058-2.411-7.085-.149-.19-.366-.315-.603-.344-.05-.006-.097-.011-.146-.006ZM7.092 3.742a.9272.9272 0 0 0-.631.29C5.169 5.436 4.447 7.28 4.447 9.185c0 2.003.792 3.917 2.204 5.345.354.356.935.361 1.293.007.356-.357.359-.933.003-1.287-1.068-1.084-1.674-2.542-1.674-4.065 0-1.447.549-2.848 1.529-3.913.339-.369.313-.945-.055-1.288-.175-.161-.412-.252-.655-.242Zm9.904 0c-.242-.01-.475.081-.654.242-.368.343-.394.919-.055 1.288.982 1.065 1.531 2.466 1.531 3.913 0 1.523-.599 2.981-1.671 4.065-.35.356-.35.93.009 1.287.354.349.931.346 1.286-.007 1.408-1.428 2.202-3.342 2.202-5.345 0-1.905-.723-3.749-2.014-5.153a.9197.9197 0 0 0-.634-.29Zm-4.951 1.71c-2.047 0-3.724 1.684-3.724 3.733 0 1.736 1.203 3.206 2.815 3.61v8.776c0 .509.405.919.909.919.506.002.918-.41.918-.919v-8.776c1.612-.405 2.816-1.874 2.816-3.61 0-2.049-1.687-3.733-3.734-3.733Zm0 1.828c1.063 0 1.906.844 1.906 1.905 0 1.063-.843 1.901-1.906 1.901s-1.899-.838-1.899-1.901c0-1.061.836-1.905 1.899-1.905Z"
      />
    </svg>
  )
}
