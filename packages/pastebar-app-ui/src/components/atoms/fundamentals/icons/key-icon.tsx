import React from 'react'

import IconProps from './types/icon-type'

const KeyIcon: React.FC<IconProps> = ({
  size = '32px',
  color = 'currentColor',
  ...attributes
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...attributes}
    >
      <path
        d="M18.9095 10.761C19.5268 10.761 20.1188 11.0063 20.5553 11.4427C20.9918 11.8792 21.237 12.4713 21.237 13.0886M23.5646 13.0886C23.5647 13.7675 23.4162 14.4383 23.1297 15.0538C22.8432 15.6693 22.4255 16.2147 21.9059 16.6518C21.3863 17.0888 20.7773 17.4069 20.1218 17.5837C19.4663 17.7605 18.78 17.7918 18.1111 17.6754C17.6743 17.6001 17.2119 17.6955 16.8985 18.009L14.8363 20.0712H13.0906V21.8168H11.345V23.5625H8.43555V21.3762C8.43555 20.913 8.61942 20.4684 8.94683 20.1418L13.9891 15.0996C14.3025 14.7861 14.3979 14.3237 14.3227 13.8869C14.2127 13.2516 14.2358 12.6004 14.3907 11.9745C14.5455 11.3487 14.8287 10.7618 15.2222 10.2511C15.6158 9.74039 16.1112 9.31702 16.6769 9.0078C17.2427 8.69858 17.8665 8.51026 18.5088 8.45477C19.1512 8.39928 19.7981 8.47784 20.4085 8.68545C21.0189 8.89307 21.5795 9.22523 22.0548 9.66087C22.5301 10.0965 22.9097 10.6261 23.1696 11.2162C23.4295 11.8062 23.564 12.4438 23.5646 13.0886V13.0886Z"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default KeyIcon
