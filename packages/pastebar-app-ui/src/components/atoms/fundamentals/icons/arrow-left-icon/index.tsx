import React from 'react'

import IconProps from '../types/icon-type'

const ArrowLeftIcon: React.FC<IconProps> = ({
  size = '20',
  color = 'currentColor',
  ...attributes
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...attributes}
    >
      <path d="M3.75 10H16.875" stroke={color} strokeWidth="1.5" strokeLinecap="round" />
      <path
        d="M8.125 5L3.125 10L8.125 15"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default ArrowLeftIcon
