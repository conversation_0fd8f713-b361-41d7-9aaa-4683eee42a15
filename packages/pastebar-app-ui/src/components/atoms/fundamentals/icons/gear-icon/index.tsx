import React from 'react'

import IconProps from '../types/icon-type'

const GearIcon: React.FC<IconProps> = ({
  size = '24',
  color = 'currentColor',
  ...attributes
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...attributes}
    >
      <path
        d="M12.1952 3H11.8048C11.3342 3 10.8829 3.18964 10.5502 3.52721C10.2174 3.86477 10.0305 4.32261 10.0305 4.8V4.962C10.0302 5.27765 9.94806 5.58767 9.79235 5.86095C9.63663 6.13424 9.41282 6.36117 9.14336 6.519L8.76188 6.744C8.49215 6.90198 8.18618 6.98515 7.87472 6.98515C7.56327 6.98515 7.2573 6.90198 6.98757 6.744L6.8545 6.672C6.44735 6.43374 5.96365 6.3691 5.50957 6.49228C5.05549 6.61546 4.66815 6.91638 4.43256 7.329L4.23739 7.671C4.00252 8.08404 3.93881 8.57475 4.06023 9.0354C4.18165 9.49605 4.47828 9.889 4.88501 10.128L5.01808 10.218C5.28625 10.3751 5.50923 10.6006 5.66486 10.8721C5.8205 11.1437 5.90337 11.4519 5.90524 11.766V12.225C5.90648 12.5422 5.82508 12.8541 5.66929 13.1291C5.5135 13.4041 5.28885 13.6324 5.01808 13.791L4.88501 13.872C4.47828 14.111 4.18165 14.5039 4.06023 14.9646C3.93881 15.4253 4.00252 15.916 4.23739 16.329L4.43256 16.671C4.66815 17.0836 5.05549 17.3845 5.50957 17.5077C5.96365 17.6309 6.44735 17.5663 6.8545 17.328L6.98757 17.256C7.2573 17.098 7.56327 17.0148 7.87472 17.0148C8.18618 17.0148 8.49215 17.098 8.76188 17.256L9.14336 17.481C9.41282 17.6388 9.63663 17.8658 9.79235 18.139C9.94806 18.4123 10.0302 18.7223 10.0305 19.038V19.2C10.0305 19.6774 10.2174 20.1352 10.5502 20.4728C10.8829 20.8104 11.3342 21 11.8048 21H12.1952C12.6658 21 13.1171 20.8104 13.4498 20.4728C13.7826 20.1352 13.9695 19.6774 13.9695 19.2V19.038C13.9698 18.7223 14.0519 18.4123 14.2077 18.139C14.3634 17.8658 14.5872 17.6388 14.8566 17.481L15.2381 17.256C15.5078 17.098 15.8138 17.0148 16.1253 17.0148C16.4367 17.0148 16.7427 17.098 17.0124 17.256L17.1455 17.328C17.5527 17.5663 18.0364 17.6309 18.4904 17.5077C18.9445 17.3845 19.3319 17.0836 19.5674 16.671L19.7626 16.32C19.9975 15.907 20.0612 15.4163 19.9398 14.9556C19.8184 14.4949 19.5217 14.102 19.115 13.863L18.9819 13.791C18.7111 13.6324 18.4865 13.4041 18.3307 13.1291C18.1749 12.8541 18.0935 12.5422 18.0948 12.225V11.775C18.0935 11.4578 18.1749 11.1459 18.3307 10.8709C18.4865 10.5959 18.7111 10.3676 18.9819 10.209L19.115 10.128C19.5217 9.889 19.8184 9.49605 19.9398 9.0354C20.0612 8.57475 19.9975 8.08404 19.7626 7.671L19.5674 7.329C19.3319 6.91638 18.9445 6.61546 18.4904 6.49228C18.0364 6.3691 17.5527 6.43374 17.1455 6.672L17.0124 6.744C16.7427 6.90198 16.4367 6.98515 16.1253 6.98515C15.8138 6.98515 15.5078 6.90198 15.2381 6.744L14.8566 6.519C14.5872 6.36117 14.3634 6.13424 14.2077 5.86095C14.0519 5.58767 13.9698 5.27765 13.9695 4.962V4.8C13.9695 4.32261 13.7826 3.86477 13.4498 3.52721C13.1171 3.18964 12.6658 3 12.1952 3Z"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 14C13.1046 14 14 13.1046 14 12C14 10.8954 13.1046 10 12 10C10.8954 10 10 10.8954 10 12C10 13.1046 10.8954 14 12 14Z"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default GearIcon
